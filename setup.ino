void saveConfigCallback() {
  Serial.println("Should save config");
  shouldSaveConfig = true;
}
void setup() {

  pinMode(fecha_valvula_1, OUTPUT);
  pinMode(pwmPin, OUTPUT);
  analogWrite(pwmPin, 0);
  digitalWrite(pwmPin, HIGH);
  pinMode(abre_valvula_1, OUTPUT);
  pinMode(enable, OUTPUT);
  digitalWrite(enable, LOW);
  digitalWrite(abre_valvula_1, LOW);
  digitalWrite(fecha_valvula_1, LOW);
  pinMode(valvula_fechada_1, INPUT);
  pinMode(valvula_aberta_1, INPUT);
  pinMode(botao, INPUT);
  sizeof_eeprom = sizeof(struct eeprom);
  sizeof_eeprom_param = sizeof(struct eeprom_par);
  Serial.begin(115200);
  WiFi.mode(WIFI_STA);
  WiFi.begin(ssid, password);

  while (WiFi.status() != WL_CONNECTED) {
    Serial.print(".");
    delay(500);
  }
  Serial.println("");
  Serial.println("WiFi connected");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());
  EEPROM.begin(eepromSize);
  getDevice();
  eepromRead();
  attachInterrupt(digitalPinToInterrupt(valvula_aberta_1), abriu, FALLING);  //falling
  attachInterrupt(digitalPinToInterrupt(valvula_fechada_1), fechou, FALLING);
  if (button.onReleased()) {
    1 + 1;
  }

  WiFi.hostname(eeprom_parametros.dispositivo);
  gotIpEventHandler = WiFi.onStationModeGotIP([](const WiFiEventStationModeGotIP& event) {
    Serial.print("Station connected, IP: ");
    Serial.println(WiFi.localIP());
    wifiConnected = true;
  });

  disconnectedEventHandler = WiFi.onStationModeDisconnected([](const WiFiEventStationModeDisconnected& event) {
    if (conta_loop_reset > 5) {
      ESP.restart();
    }
    conta_loop_reset++;
    Serial.println((String) "Station disconnected " + conta_loop_reset);
  });

  mqttClient.setServer(eeprom_parametros.mqtt_server, atoi(eeprom_parametros.mqtt_port));
  mqttClient.setCallback(callback);
  mqttClient.setBufferSize(512);
  mqttClient.setKeepAlive(1);

  malvadeza.setTimeout(3000);
  timeOffabre_valvula_1.setTimeout(2800);
  timeOfffecha_valvula_1.setTimeout(2800);
  timeOfffecha_valvula_1.restart();
  timeOffabre_valvula_1.restart();
  timeReconnectMqtt.setTimeout(1000);
  timeReconnectMqtt.restart();
  ElegantOTA.begin(&server);
  server.on("/", handleRoot);  //Which routine to handle at root location. This is display page
  server.on("/apagar", handleApagar);
  // server.on("/setName", handleName);
  server.on("/mqtt", handleServerPort);
  // server.on("/seteeprom_parametros.dispositivo", handleValvula);
  server.on("/readMsg", handleMsg);
  server.on("/sendTitle", handleTitle);
  server.on("/reset", handleReset);

  server.begin();
  MDNS.begin(eeprom_parametros.dispositivo);
  MDNS.addService("http", "tcp", 80);
}
