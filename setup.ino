void saveConfigCallback() {
  Serial.println("Should save config");
  shouldSaveConfig = true;
}
void setup() {

  pinMode(fecha_valvula_1, OUTPUT);
  pinMode(pwmPin, OUTPUT);
  analogWrite(pwmPin, 0);
  digitalWrite(pwmPin, HIGH);
  pinMode(abre_valvula_1, OUTPUT);
  pinMode(enable, OUTPUT);
  digitalWrite(enable, LOW);
  digitalWrite(abre_valvula_1, LOW);
  digitalWrite(fecha_valvula_1, LOW);
  pinMode(valvula_fechada_1, INPUT);
  pinMode(valvula_aberta_1, INPUT);
  pinMode(botao, INPUT);
  sizeof_eeprom = sizeof(struct eeprom);
  sizeof_eeprom_param = sizeof(struct eeprom_par);
  Serial.begin(115200);

  // Configuração como Access Point
  WiFi.mode(WIFI_AP);
  WiFi.softAP("VALVULA_EVO");  // Rede sem senha

  // Configuração do IP do AP
  IPAddress local_IP(192, 168, 4, 1);
  IPAddress gateway(192, 168, 4, 1);
  IPAddress subnet(255, 255, 255, 0);
  WiFi.softAPConfig(local_IP, gateway, subnet);

  Serial.println("");
  Serial.println("Access Point iniciado");
  Serial.print("Nome da rede: VALVULA_EVO");
  Serial.print("IP do Access Point: ");
  Serial.println(WiFi.softAPIP());
  EEPROM.begin(eepromSize);
  getDevice();
  eepromRead();
  attachInterrupt(digitalPinToInterrupt(valvula_aberta_1), abriu, FALLING);  //falling
  attachInterrupt(digitalPinToInterrupt(valvula_fechada_1), fechou, FALLING);
  if (button.onReleased()) {
    1 + 1;
  }

  // No modo AP, não precisamos dos event handlers de Station Mode
  // WiFi.hostname(eeprom_parametros.dispositivo);  // Não aplicável no modo AP
  wifiConnected = true;  // Sempre "conectado" no modo AP

  // MQTT desabilitado no modo Access Point (sem conectividade externa)
  // mqttClient.setServer(eeprom_parametros.mqtt_server, atoi(eeprom_parametros.mqtt_port));
  // mqttClient.setCallback(callback);
  // mqttClient.setBufferSize(512);
  // mqttClient.setKeepAlive(1);

  malvadeza.setTimeout(3000);
  timeOffabre_valvula_1.setTimeout(2800);
  timeOfffecha_valvula_1.setTimeout(2800);
  timeOfffecha_valvula_1.restart();
  timeOffabre_valvula_1.restart();
  timeReconnectMqtt.setTimeout(1000);
  timeReconnectMqtt.restart();
  ElegantOTA.begin(&server);
  server.on("/", handleRoot);  //Which routine to handle at root location. This is display page
  server.on("/apagar", handleApagar);
  // server.on("/setName", handleName);
  server.on("/mqtt", handleServerPort);
  server.on("/setValvula", handleValvula);  // Reativado para controles
  server.on("/readMsg", handleMsg);
  server.on("/sendTitle", handleTitle);
  server.on("/reset", handleReset);
  server.on("/setModo", handleSetModo);     // NOVO: Configurar modo
  server.on("/getModo", handleGetModo);     // NOVO: Obter modo atual

  server.begin();
  MDNS.begin(eeprom_parametros.dispositivo);
  MDNS.addService("http", "tcp", 80);
}
