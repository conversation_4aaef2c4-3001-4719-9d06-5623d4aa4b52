void getDevice() {
  eeprom_par eeprom_temp;
  MAC.substring(9, MAC.length()).toCharArray(text, 30);
  strcat(text, "V_SIMP_CAB");
  str_replace(text, ":", "");
  Serial.println(text);
  EEPROM.get(0, eeprom_parametros);
  atualiza_tamanho_eeprom();
#ifdef NOME_MANUAL
  //ALTERAR A PORTA E O SERVIDOR NESTE CAMPO
  strcpy(eeprom_parametros.dispositivo, NOME_MANUAL);
  strcpy(eeprom_parametros.mqtt_server, "MCC.local");
  strcpy(eeprom_parametros.mqtt_port, "1883");
#else
  if (!strstr(eeprom_parametros.dispositivo, "VALVULA") && !strstr(eeprom_parametros.dispositivo, "V_SIMP_CAB")) {
    strcpy(eeprom_parametros.dispositivo, text);
    strcpy(eeprom_parametros.mqtt_server, "MCC.local");
    strcpy(eeprom_parametros.mqtt_port, "1883");
    eeprom_parametros.habilita_botao = true;
    eeprom_parametros.is_root = false;
    EEPROM.put(0, eeprom_parametros);
    EEPROM.commit();
    zera_eeprom();
    ESP.restart();
  }
#endif
  if (strstr(eeprom_parametros.dispositivo, "CABINE")) {
    valvula_cabine = true;
  }
}
void atualiza_tamanho_eeprom() {
  if (eeprom_parametros.version != CURRENT_VERSION_STRUCT) {
    Serial.println("Versão da EEPROM incompatível, gravando novos dados");
    // Configura dados padrão se a versão não for a atual
    eeprom eepromVar_temp;
    EEPROM.get(sizeof_struct_ant, eepromVar_temp);
    EEPROM.put(sizeof_eeprom_param, eepromVar_temp);
    EEPROM.commit();
    eeprom_parametros.is_root = false;
    eeprom_parametros.version = CURRENT_VERSION_STRUCT;
    EEPROM.put(0, eeprom_parametros);
    EEPROM.commit();
    Serial.println("REINICIANDO ESP");
    ESP.restart();
  } else {
    Serial.printf("EEPROM NA VERSÃO %d\n", CURRENT_VERSION_STRUCT);
  }
}
void eepromRead() {
  int tamanho = sizeof(struct eeprom);
  eeprom eepromVar_temp;
  eeprom eepromVar_temp2;
  EEPROM.get(sizeof_eeprom_param, eepromVar_temp);
  previousIndex = eepromVar_temp.index;
  Serial.println();
  for (int a = sizeof_eeprom_param; a < eepromSize; a += tamanho) {
    EEPROM.get(a, eepromVar_temp2);
    if (previousIndex != eepromVar_temp2.index) {
      EEPROM.get(a - tamanho, eepromVar);
      break;
    } else {
      previousIndex += tamanho;
    }
  }
}
bool eepromWrite() {
  int tamanho = sizeof(struct eeprom);
  bool canal_encontrado = false;
  eeprom eepromVar_temp;
  eeprom eepromVar_temp2;
  EEPROM.get(sizeof_eeprom_param, eepromVar_temp);
  previousIndex = eepromVar_temp.index;
  for (int a = sizeof_eeprom_param; a < eepromSize; a += tamanho) {
    EEPROM.get(a, eepromVar_temp2);
    if (previousIndex != eepromVar_temp2.index) {
      eepromVar.index = previousIndex;
      EEPROM.put(a, eepromVar);
      EEPROM.commit();
      Serial.println((String) "Size of eeprom " + tamanho);
      return true;
    } else {
      previousIndex += tamanho;
    }
  }
  // caso estoure os endereços da eeprom será gravado o total no endereço 0
  loop_eeprom();
  return true;
}

void zera_eeprom() {
  int tamanho = sizeof(struct eeprom);
  byte index_temp;
  eeprom eepromVar_temp;
  EEPROM.get(sizeof_eeprom_param, eepromVar_temp);
  if (eepromVar_temp.index >= 1) {
    index_temp = 0;
  } else {
    index_temp = 1;
  }
  eepromVar.operacoes = 0;
  eepromVar.index = index_temp;
  EEPROM.put(sizeof_eeprom_param, eepromVar);
  EEPROM.commit();
  EEPROM.get(sizeof_eeprom_param, eepromVar);
}
void loop_eeprom() {
  int tamanho = sizeof(struct eeprom);
  byte index_temp;
  eeprom eepromVar_temp;
  EEPROM.get(sizeof_eeprom_param, eepromVar_temp);
  Serial.println(eepromVar_temp.index);
  if (eepromVar_temp.index >= 1) {
    index_temp = 0;
  } else {
    index_temp = 1;
  }
  eepromVar.index = index_temp;
  EEPROM.put(sizeof_eeprom_param, eepromVar);
  EEPROM.commit();
  EEPROM.get(sizeof_eeprom_param, eepromVar);
}

void str_replace(char *src, char *oldchars, char *newchars) {  // utility string function
  char *p = strstr(src, oldchars);
  char buf[30];
  do {
    if (p) {
      memset(buf, '\0', strlen(buf));
      if (src == p) {
        strcpy(buf, newchars);
        strcat(buf, p + strlen(oldchars));
      } else {
        strncpy(buf, src, strlen(src) - strlen(p));
        strcat(buf, newchars);
        strcat(buf, p + strlen(oldchars));
      }
      memset(src, '\0', strlen(src));
      strcpy(src, buf);
    }
  } while (p && (p = strstr(src, oldchars)));
}

void writePrefixoEten(int addr, const char *data) {
  int len = strlen(data);
  for (int i = 0; i < len; i++) {
    EEPROM.write(addr + i, data[i]);
  }
  EEPROM.write(addr + len, '\0');  // Adicionar terminador nulo
  EEPROM.commit();                 // Necessário em ESP8266
}

String readPrefixoEten(int addr) {
  String result;
  char c;
  while ((c = EEPROM.read(addr++)) != '\0') {
    result += c;
  }
  return result;
}