void callback(char* topic, byte* message, unsigned int length) {
  String msgReceived;

  for (int i = 0; i < length; i++) {
    msgReceived += (char)message[i];
  }
  if (msgReceived.indexOf("GMAF") > -1) {
    send_calib();
  }
  if (msgReceived.indexOf("CMAF") > -1) {
    int index1 = msgReceived.indexOf(',');
    int index2 = msgReceived.indexOf(',', index1 + 1);
    if (isValidNumber(msgReceived.substring(index1 + 1, index2)) && msgReceived.substring(index2 + 1, msgReceived.length())) {

      int index = msgReceived.substring(index1 + 1, index2).toInt();
      double valor = msgReceived.substring(index2 + 1, msgReceived.length()).toDouble();
      if (index <= sizeof(eeprom_parametros.nodepoints) / sizeof(eeprom_parametros.nodepoints[0])) {
        for (int a = 1; a < index; a++) {
          if (media_maf < eeprom_parametros.nodepoints[a][0]) {
            send_mqtt("S_ERRO CALIB", (String) "ERRO " + media_maf + " menor que index" + a + " de valor: " + eeprom_parametros.nodepoints[a][0]);
            return;
          }
        }

        eeprom_parametros.nodepoints[index][0] = media_maf;
        eeprom_parametros.nodepoints[index][1] = valor;

        EEPROM.put(0, eeprom_parametros);
        EEPROM.commit();
        send_calib();
      }
    } else {
      Serial.println((String) "DADOS INVALIDOS " + msgReceived);
        send_mqtt("S_ERRO CALIB", "DADOS INVALIDOS " + msgReceived);
    }
  }

  if (msgReceived.indexOf("HAB_BOT") > -1) {
    eeprom_parametros.habilita_botao = !eeprom_parametros.habilita_botao;
    EEPROM.put(0, eeprom_parametros);
    EEPROM.commit();
  }
  if (msgReceived.indexOf("DEBUG") > -1) {
    debug_code = !debug_code;
  }
  if (msgReceived.indexOf("ZERA_M3") > -1) {
    soma_metro_cubico_ar = 0.0;
  }


  if (msgReceived.indexOf("OPERACOES") == 0) {
    eepromVar.operacoes++;
    eepromWrite();
    //eepromRead();
  }
  if (msgReceived.indexOf("ZERA_OPERACOES") == 0) {
    zera_eeprom();
  }

  if (msgReceived == "LOOP") {
    teste = !teste;
    operacoes_teste = eepromVar.operacoes;
  }

  if (msgReceived == "RESET") {
    ESP.restart();
  }

  if (msgReceived.indexOf("ABRE") > -1) {
    if (digitalRead(valvula_aberta_1) == HIGH) {

      abre();

    } else {
      send_mqtt("S_TEMPO_OPERACAO", "ABRE= 0");
    }
  }

  if (msgReceived.indexOf("FECHA") > -1) {
    if (digitalRead(valvula_fechada_1) == HIGH) {
      fecha();
    } else {
      send_mqtt("S_TEMPO_OPERACAO", "FECHA= 0");
    }
  }
  #ifndef NOME_MANUAL
    if (msgReceived.indexOf("NOME") > -1 && msgReceived.length() > 10) {
      byte index1 = msgReceived.indexOf(',');
      String nome = msgReceived.substring(index1 + 1, msgReceived.length());
      nome.toCharArray(text, 30);
      strcpy(eeprom_parametros.dispositivo, text);
      EEPROM.put(0, eeprom_parametros);
      EEPROM.commit();
      ESP.restart();
    }
  #endif
}
void reconnectMqtt() {
  // Loop until we're reconnected

  if (!mqttClient.connected()) {
    //     Serial.print("Attempting MQTT connection...");
    // Attempt to connect
    if (mqttClient.connect(eeprom_parametros.dispositivo)) {
      Serial.println("Mqtt conectado");
      // Subscribe
      mqttClient.subscribe(eeprom_parametros.dispositivo);
    }
  }
}
boolean isValidNumber(String str) {
  for (byte i = 0; i < str.length(); i++) {
    if (!isDigit(str.charAt(i))) return false;
  }
  return true;
}