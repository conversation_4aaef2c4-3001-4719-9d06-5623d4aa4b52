void handleRoot() {
  String s = MAIN_page;              //Read HTML contents
  server.send(200, "text/html", s);  //Send web page
}
void handleReset() {
  server.send(200, "text/plane", "RESETANDO...");
  delay(3000);
  ESP.restart();
}
void handleName() {
  String t_state = server.arg("Name");
  char newName[30] = "";
  t_state.toCharArray(newName, 30);
  strcpy(eeprom_parametros.dispositivo, newName);
  EEPROM.put(0, eeprom_parametros);
  EEPROM.commit();
  String ipaddress = WiFi.localIP().toString();
  char ipchar[ipaddress.length() + 1];
  ipaddress.toCharArray(ipchar, ipaddress.length() + 1);
  server.send(200, "text/plane", (String) "DISPOTITIVO = " + eeprom_parametros.dispositivo + "\n"
                                                                                             "IP = "
                                   + ipchar + "\n"
                                              "STATUS VALVULA = "
                                   + statusValvula_1 + "\n"
                                                       "STATUS MQTT = "
                                   + mqttClient.connected() + "\n"
                                                          "SERVIDOR MQTT = "
                                   + eeprom_parametros.mqtt_server + "\n"
                                                                     "PORTA MQTT = "
                                   + eeprom_parametros.mqtt_port + "\n"
                                                                   "OPERACOES = "
                                   + eepromVar.operacoes + "\n");
  delay(3000);
  ESP.restart();
}
void handleMsg() {
String ipaddress = WiFi.localIP().toString();

String response = "DISPOSITIVO COM CONFIGURAÇÕES FIXAS\n"
                  "DISPOSITIVO = " + String(eeprom_parametros.dispositivo) + "\n" +
                  "IP = " + ipaddress + "\n" +
                  "STATUS = " + String(statusValvula_1) + "\n" +
                  "STATUS MQTT = " + String(mqttClient.connected() ? "Conectado" : "Desconectado") + "\n" +
                  "SERVIDOR MQTT = " + String(eeprom_parametros.mqtt_server) + "\n" +                
                  "PORTA MQTT = " + String(eeprom_parametros.mqtt_port) + "\n" +
                  "OPERACOES = " + String(eepromVar.operacoes) + "\n";

server.send(200, "text/plain", response);
}
void handleTitle() {
  server.send(200, "text/plane", (String)eeprom_parametros.dispositivo);
}
void handleValvula() {
  String t_state = server.arg("Comando");  //refer xhttp.open("GET", "seteeprom_parametros.dispositivo?Commando="+name, true);

  if (t_state == "abrir") {
    if (digitalRead(valvula_aberta_1) == NAO_ATUADO) {  //aberta, fechaporta
      abre();
    }
  }
  if (t_state == "fechar") {
    if (digitalRead(valvula_fechada_1) == NAO_ATUADO) {  //aberta, fechaporta
      fecha();
    }
  }
  if (t_state == "teste") {
    teste = true;
    operacoes_teste = eepromVar.operacoes;
  }
  if (t_state == "parar") {
    teste = false;
  }
  if (t_state == "zerar") {
    zera_eeprom();
  }
  server.send(200, "text/plane", (String) "STATUS = " + statusValvula_1 + "\n");  //Send web page
}
void handleApagar() {
  server.send(200, "text/plane", (String) "Redefinindo dispositivo, entre no AcessPoint " + eeprom_parametros.dispositivo + " e reconfigure-o");
  delay(3000);
  // wifiManager.resetSettings();
  delay(1000);
  ESP.restart();
}
void handleServerPort() {
  String serverMqtt = server.arg("server");  //refer xhttp.open("GET", "mqtt?server=<>&port=<>
  String portMqtt = server.arg("port");      ////refer xhttp.open("GET", "mqtt?server=<>&port=<>
  String ipaddress = WiFi.localIP().toString();
  char ipchar[ipaddress.length() + 1];
  ipaddress.toCharArray(ipchar, ipaddress.length() + 1);
  server.send(200, "text/plane", (String) 
      "DISPOSITIVO COM CONFIGURAÇÕES FIXAS\n"
      "DISPOSITIVO = " + eeprom_parametros.dispositivo + "\n"
      "IP = " + ipaddress + "\n"
      "STATUS  = " + statusValvula_1 + "\n"
      "STATUS MQTT = " + mqttClient.connected() + "\n"
      "SERVIDOR MQTT = " + String(eeprom_parametros.mqtt_server) + "\n"
      "PORTA MQTT = " + eeprom_parametros.mqtt_port + "\n"
      "OPERACOES = " + eepromVar.operacoes + "\n");
}
