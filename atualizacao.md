# Plano de Atualização - Sistema de Controle de Válvula/Cabine IoT
## Migração para Access Point com Interface Web Unificada

---

## 1. VISÃO GERAL DA ATUALIZAÇÃO

### 1.1 Objetivo Principal
Transformar o sistema atual de conectividade WiFi cliente para um modelo **Access Point (AP)** autônomo, eliminando a dependência de redes WiFi externas e criando uma interface web unificada para ambos os modos de operação.

### 1.2 Benefícios da Mudança
- **Autonomia Total**: Não depende de infraestrutura WiFi externa
- **Simplicidade de Acesso**: Conexão direta ao dispositivo
- **Interface Unificada**: Um único código para ambos os modos
- **Configuração Dinâmica**: Seleção do modo via interface web
- **Portabilidade**: Funciona em qualquer local sem configuração prévia

---

## 2. ARQUITETURA PROPOSTA

### 2.1 Modelo de Conectividade
```
[Dispositivo Móvel/Laptop] 
         ↓ WiFi
[ESP8266 - Access Point]
         ↓ Interface Web
[Controle Válvula/Cabine]
```

### 2.2 Fluxo de Operação
1. **Inicialização**: ESP8266 cria rede WiFi própria
2. **Conexão**: Usuário conecta ao WiFi do dispositivo
3. **Redirecionamento**: Portal captivo abre automaticamente a interface
4. **Seleção**: Usuário escolhe modo (Válvula Simples/Cabine)
5. **Controle**: Interface adapta-se ao modo selecionado

---

## 3. ALTERAÇÕES NO CÓDIGO ATUAL

### 3.1 Configuração WiFi - PRINCIPAIS MUDANÇAS

#### **REMOVER:**
```cpp
// Configuração WiFi Cliente (ATUAL)
const char* ssid = "mcc1";
const char* password = "##mcc1##";
WiFi.mode(WIFI_STA);
WiFi.begin(ssid, password);
```

#### **ADICIONAR:**
```cpp
// Configuração Access Point (NOVO)
const char* ap_ssid = "VALVULA_CONFIG";
const char* ap_password = "";  // Sem senha
WiFi.mode(WIFI_AP);
WiFi.softAP(ap_ssid, ap_password);
IPAddress local_IP(192,168,4,1);
IPAddress gateway(192,168,4,1);
IPAddress subnet(255,255,255,0);
WiFi.softAPConfig(local_IP, gateway, subnet);
```

### 3.2 Sistema MQTT - MODIFICAÇÕES

#### **ESTRATÉGIA:**
- **Manter funcionalidade MQTT** para compatibilidade
- **Tornar opcional** baseado em configuração
- **Adicionar configuração via web** para servidor MQTT externo

#### **IMPLEMENTAÇÃO:**
```cpp
// Variável de controle
bool mqtt_enabled = false;
char external_mqtt_server[25] = "";
char external_mqtt_port[6] = "";

// Função de habilitação condicional
void setupMQTT() {
  if (mqtt_enabled && strlen(external_mqtt_server) > 0) {
    mqttClient.setServer(external_mqtt_server, atoi(external_mqtt_port));
    // ... resto da configuração MQTT
  }
}
```

### 3.3 Interface Web - REFATORAÇÃO COMPLETA

#### **ESTRUTURA ATUAL (progmem.h):**
- Interface estática com funcionalidades fixas
- Modo determinado pelo nome do dispositivo

#### **NOVA ESTRUTURA PROPOSTA:**
```html
<!-- Página Principal com Seleção de Modo -->
<select id="modoOperacao">
  <option value="simples" selected>Válvula Simples</option>
  <option value="cabine">Válvula Cabine</option>
</select>

<!-- Container Dinâmico -->
<div id="interface-container">
  <!-- Conteúdo carregado dinamicamente via JavaScript -->
</div>
```

---

## 4. IMPLEMENTAÇÃO DETALHADA

### 4.1 Modificações no Arquivo Principal

#### **V_CAB_SIMP_NOME_FIXO.ino - Alterações:**

1. **Variáveis Globais Adicionais:**
```cpp
// Controle de modo dinâmico
enum ModoOperacao { MODO_SIMPLES, MODO_CABINE };
ModoOperacao modo_atual = MODO_SIMPLES;
bool modo_alterado = false;

// Configuração AP
const char* ap_ssid = "VALVULA_CONFIG";
IPAddress ap_ip(192, 168, 4, 1);
```

2. **Remoção da Detecção Automática:**
```cpp
// REMOVER esta lógica do getDevice():
if (strstr(eeprom_parametros.dispositivo, "CABINE")) {
    valvula_cabine = true;
}

// SUBSTITUIR por:
valvula_cabine = (modo_atual == MODO_CABINE);
```

### 4.2 Modificações no Setup (setup.ino)

#### **ALTERAÇÕES PRINCIPAIS:**

1. **Configuração WiFi AP:**
```cpp
void setup() {
  // ... configurações de pinos existentes ...
  
  // NOVA configuração WiFi
  WiFi.mode(WIFI_AP);
  WiFi.softAP(ap_ssid);
  WiFi.softAPConfig(ap_ip, ap_ip, IPAddress(255, 255, 255, 0));
  
  // Portal Captivo
  dnsServer.start(53, "*", ap_ip);
  
  // ... resto do setup ...
}
```

2. **Novos Endpoints Web:**
```cpp
server.on("/", handleRoot);
server.on("/setModo", handleSetModo);        // NOVO
server.on("/getStatus", handleGetStatus);    // NOVO  
server.on("/getModoAtual", handleGetModo);   // NOVO
server.onNotFound(handleRoot);               // Portal Captivo
```

### 4.3 Novo Arquivo: portal_captivo.ino

#### **FUNCIONALIDADES:**
```cpp
#include <DNSServer.h>
DNSServer dnsServer;

void setupCaptivePortal() {
  dnsServer.start(53, "*", WiFi.softAPIP());
}

void handleCaptivePortal() {
  dnsServer.processNextRequest();
}
```

### 4.4 Refatoração do WebServer (webserver.ino)

#### **NOVOS HANDLERS:**

1. **handleSetModo():**
```cpp
void handleSetModo() {
  String modo = server.arg("modo");
  if (modo == "simples") {
    modo_atual = MODO_SIMPLES;
    valvula_cabine = false;
  } else if (modo == "cabine") {
    modo_atual = MODO_CABINE;
    valvula_cabine = true;
  }
  modo_alterado = true;
  server.send(200, "application/json", "{\"status\":\"ok\"}");
}
```

2. **handleGetStatus():**
```cpp
void handleGetStatus() {
  DynamicJsonDocument doc(1024);
  
  // Status comum
  doc["modo"] = (modo_atual == MODO_SIMPLES) ? "simples" : "cabine";
  doc["valvula"] = statusValvula_1;
  doc["operacoes"] = eepromVar.operacoes;
  doc["uptime"] = getUptimeString();
  
  // Status específico modo cabine
  if (modo_atual == MODO_CABINE) {
    doc["maf"]["tensao"] = voltage_sensor;
    doc["maf"]["velocidade"] = velocidade_ar;
    doc["maf"]["volume_s"] = metro_cubico_s_ar;
    doc["maf"]["volume_h"] = metro_cubico_s_ar * 3600;
    doc["maf"]["acumulado"] = soma_metro_cubico_ar;
  }
  
  String response;
  serializeJson(doc, response);
  server.send(200, "application/json", response);
}
```

### 4.5 Nova Interface HTML (progmem.h)

#### **ESTRUTURA RESPONSIVA:**
```html
<!DOCTYPE html>
<html>
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Controle de Válvula</title>
  <style>
    /* CSS responsivo para mobile */
    body { font-family: Arial; margin: 20px; }
    .container { max-width: 600px; margin: 0 auto; }
    .modo-selector { margin-bottom: 20px; }
    .status-grid { display: grid; gap: 10px; }
    .button { padding: 15px; font-size: 16px; margin: 5px; }
    .status-item { padding: 10px; border: 1px solid #ccc; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Controle de Válvula IoT</h1>
    
    <!-- Seletor de Modo -->
    <div class="modo-selector">
      <label>Modo de Operação:</label>
      <select id="modoSelect" onchange="alterarModo()">
        <option value="simples">Válvula Simples</option>
        <option value="cabine">Válvula Cabine</option>
      </select>
    </div>
    
    <!-- Status Dinâmico -->
    <div id="status-container"></div>
    
    <!-- Controles Dinâmicos -->
    <div id="controles-container"></div>
  </div>
  
  <script>
    // JavaScript para interface dinâmica
    let modoAtual = 'simples';
    
    function alterarModo() {
      const novoModo = document.getElementById('modoSelect').value;
      fetch('/setModo?modo=' + novoModo)
        .then(() => {
          modoAtual = novoModo;
          atualizarInterface();
        });
    }
    
    function atualizarInterface() {
      // Atualiza interface baseada no modo
      if (modoAtual === 'simples') {
        mostrarInterfaceSimples();
      } else {
        mostrarInterfaceCabine();
      }
    }
    
    function mostrarInterfaceSimples() {
      document.getElementById('controles-container').innerHTML = `
        <button class="button" onclick="enviarComando('abre')">ABRIR</button>
        <button class="button" onclick="enviarComando('fecha')">FECHAR</button>
        <button class="button" onclick="enviarComando('teste')">TESTE 100X</button>
        <button class="button" onclick="enviarComando('zerar')">ZERAR CONTADOR</button>
      `;
    }
    
    function mostrarInterfaceCabine() {
      document.getElementById('controles-container').innerHTML = `
        <button class="button" onclick="enviarComando('abre')">ABRIR</button>
        <button class="button" onclick="enviarComando('fecha')">FECHAR</button>
        <button class="button" onclick="enviarComando('teste')">TESTE 100X</button>
        <button class="button" onclick="enviarComando('zerar')">ZERAR CONTADOR</button>
        <button class="button" onclick="enviarComando('zera_m3')">ZERAR VOLUME</button>
        <button class="button" onclick="enviarComando('hab_bot')">TOGGLE BOTÃO</button>
      `;
    }
    
    function enviarComando(comando) {
      fetch('/comando?cmd=' + comando)
        .then(response => response.text())
        .then(data => console.log(data));
    }
    
    function atualizarStatus() {
      fetch('/getStatus')
        .then(response => response.json())
        .then(data => {
          let statusHtml = `
            <div class="status-grid">
              <div class="status-item">Status: ${data.valvula}</div>
              <div class="status-item">Operações: ${data.operacoes}</div>
              <div class="status-item">Uptime: ${data.uptime}</div>
          `;
          
          if (data.modo === 'cabine' && data.maf) {
            statusHtml += `
              <div class="status-item">Tensão MAF: ${data.maf.tensao}V</div>
              <div class="status-item">Velocidade: ${data.maf.velocidade} m/s</div>
              <div class="status-item">Volume/h: ${data.maf.volume_h} m³/h</div>
              <div class="status-item">Acumulado: ${data.maf.acumulado} m³</div>
            `;
          }
          
          statusHtml += '</div>';
          document.getElementById('status-container').innerHTML = statusHtml;
        });
    }
    
    // Inicialização
    atualizarInterface();
    setInterval(atualizarStatus, 2000);
  </script>
</body>
</html>
```

---

## 5. MODIFICAÇÕES NO LOOP PRINCIPAL

### 5.1 Loop.ino - Alterações Necessárias

#### **ADIÇÕES:**
```cpp
void loop() {
  // NOVO: Processar portal captivo
  dnsServer.processNextRequest();
  
  // EXISTENTE: Processamento web server
  server.handleClient();
  
  // MODIFICADO: Lógica MAF condicional
  if (modo_atual == MODO_CABINE) {
    if (timer_calcula_maf.onRestart()) {
      CALCULA_MAF();
    }
    if (leitura_sensor.onRestart()) {
      LER_MAF();
    }
  }
  
  // EXISTENTE: Controle da válvula
  valvula_1();
  
  // MODIFICADO: MQTT condicional
  if (mqtt_enabled && mqttClient.connected()) {
    // ... lógica MQTT existente ...
  }
  
  // ... resto do loop existente ...
}
```

---

## 6. ESTRUTURA DE ARQUIVOS MODIFICADA

### 6.1 Arquivos a Serem Alterados
- **V_CAB_SIMP_NOME_FIXO.ino**: Variáveis globais e configuração AP
- **setup.ino**: Configuração WiFi AP e novos endpoints
- **loop.ino**: Portal captivo e lógica condicional
- **webserver.ino**: Novos handlers e interface unificada
- **progmem.h**: Nova interface HTML responsiva

### 6.2 Novos Arquivos a Criar
- **portal_captivo.ino**: Funcionalidades do portal captivo
- **interface_dinamica.ino**: Funções auxiliares da interface

### 6.3 Arquivos Mantidos Sem Alteração
- **valvula_1.ino**: Lógica de controle da válvula
- **MAF.ino**: Algoritmos de cálculo MAF
- **EEPROM.ino**: Sistema de armazenamento
- **upTime.ino**: Cálculo de tempo de funcionamento
- **SEND_MQTT.ino**: Funções MQTT (mantidas para compatibilidade)

---

## 7. CONFIGURAÇÃO DE REDE

### 7.1 Parâmetros do Access Point
- **SSID**: "VALVULA_CONFIG" (ou baseado no MAC)
- **Senha**: Sem senha (acesso livre)
- **IP**: *********** (padrão ESP8266)
- **Gateway**: ***********
- **Subnet**: *************
- **Canal**: Automático
- **Máximo Clientes**: 4 (padrão ESP8266)

### 7.2 Portal Captivo
- **DNS Server**: Redireciona todas as consultas para o IP do ESP
- **Redirecionamento**: Qualquer URL acessa a interface principal
- **Compatibilidade**: Funciona com Android, iOS, Windows, Linux

---

## 8. VANTAGENS DA IMPLEMENTAÇÃO

### 8.1 Simplicidade Operacional
- **Zero Configuração**: Não precisa configurar WiFi
- **Acesso Imediato**: Conecta e já acessa a interface
- **Portabilidade**: Funciona em qualquer local
- **Autonomia**: Independente de infraestrutura externa

### 8.2 Flexibilidade Técnica
- **Modo Dinâmico**: Alterna entre válvula simples e cabine
- **Interface Adaptativa**: Mostra apenas controles relevantes
- **Compatibilidade**: Mantém funcionalidades MQTT opcionais
- **Expansibilidade**: Fácil adição de novos modos

### 8.3 Manutenibilidade
- **Código Unificado**: Um firmware para ambos os modos
- **Debug Simplificado**: Acesso direto sem dependências de rede
- **Atualizações**: OTA via interface web própria
- **Configuração**: Tudo via interface web intuitiva

---

## 9. CONSIDERAÇÕES DE IMPLEMENTAÇÃO

### 9.1 Limitações da ESP8266
- **Memória RAM**: Interface HTML deve ser otimizada
- **Clientes Simultâneos**: Máximo 4 conexões WiFi
- **Processamento**: JavaScript deve ser leve
- **Armazenamento**: HTML compactado no PROGMEM

### 9.2 Otimizações Necessárias
- **HTML Minificado**: Remover espaços e comentários
- **CSS Inline**: Evitar arquivos externos
- **JavaScript Essencial**: Apenas funcionalidades críticas
- **AJAX Eficiente**: Requests mínimos e dados compactos

### 9.3 Testes Recomendados
- **Múltiplos Dispositivos**: Android, iOS, Windows
- **Navegadores**: Chrome, Safari, Firefox
- **Estabilidade**: Conexões prolongadas
- **Performance**: Tempo de resposta da interface

---

*Plano de Atualização Detalhado*
*Sistema V_CAB_SIMP para Access Point Autônomo*
*Mantendo Simplicidade e Funcionalidade da ESP8266*
