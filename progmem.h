const char MAIN_page[] PROGMEM = R"=====(
<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body { font-family: Arial; margin: 20px; background-color: #f0f0f0; }
    .container { max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .modo-config { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin-bottom: 20px; border: 2px solid #2196F3; }
    .button { padding: 12px 20px; font-size: 16px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; min-width: 150px; }
    .button-primary { background-color: #2196F3; color: white; }
    .button-success { background-color: #4CAF50; color: white; }
    .button-warning { background-color: #ff9800; color: white; }
    .button-danger { background-color: #f44336; color: white; }
    .status-info { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 15px; font-family: Arial; }
    .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; margin: 10px 0; }
    .status-item { background-color: white; padding: 8px; border-radius: 3px; border: 1px solid #ddd; }
    select { padding: 8px; font-size: 16px; margin: 5px; border-radius: 3px; border: 1px solid #ccc; }
    h1 { color: #2196F3; text-align: center; }
    h3 { color: #333; margin: 10px 0 5px 0; }
    .modo-atual { font-weight: bold; color: #2196F3; }
  </style>
</head>
<body>
<title id="titulo">Controle de Válvula</title>
<div class="container">
<h1 id="texto">Controle de Válvula IoT</h1>

<!-- Configuração de Modo -->
<div class="modo-config">
  <h3>Configuração do Modo de Operação</h3>
  <label for="modoSelect">Selecione o modo:</label>
  <select id="modoSelect">
    <option value="simples">Válvula Simples</option>
    <option value="cabine">Válvula Cabine</option>
  </select>
  <button class="button button-primary" type="button" id="button_configurar">CONFIGURAR</button>
  <div style="margin-top: 10px;">
    <strong>Modo Atual:</strong> <span id="modoAtual" class="modo-atual">Carregando...</span><br>
    <strong>Dispositivo:</strong> <span id="nomeDispositivo" class="modo-atual">Carregando...</span>
  </div>
</div>

<!-- Controles da Válvula -->
<div>
  <button class="button button-success" type="button" id="button_abrir" data-name="abrir">ABRIR VÁLVULA</button><BR>
  <button class="button button-danger" type="button" id="button_fechar" data-name="fechar">FECHAR VÁLVULA</button><BR>
  <button class="button button-warning" type="button" id="button_teste" data-name="teste">TESTE 100X</button><BR>
  <button class="button button-warning" type="button" id="button_parar" data-name="parar">PARAR</button><BR>
  <button class="button button-primary" type="button" id="button_zerar" data-name="zerar">ZERAR CONTAGEM</button><BR>
</div>

<!-- Status do Sistema -->
<div class="status-info">
  <h3>Informações do Equipamento</h3>
  <div class="status-grid">
    <div class="status-item"><strong>Rede WiFi:</strong> <span id="rede">VALVULA_EVO</span></div>
    <div class="status-item"><strong>IP:</strong> <span id="ip">***********</span></div>
    <div class="status-item"><strong>MAC:</strong> <span id="mac">Carregando...</span></div>
    <div class="status-item"><strong>Uptime:</strong> <span id="uptime">Carregando...</span></div>
    <div class="status-item"><strong>Status:</strong> <span id="status_valvula">Carregando...</span></div>
    <div class="status-item"><strong>Operações:</strong> <span id="operacoes">0</span></div>
    <div class="status-item"><strong>Última Operação:</strong> <span id="ultima_operacao">Nenhuma</span></div>
  </div>

  <!-- Dados MAF (apenas modo CABINE) -->
  <div id="dados-maf" style="display: none;">
    <h3>Dados do Sensor MAF</h3>
    <div class="status-grid">
      <div class="status-item"><strong>Tensão MAF:</strong> <span id="tensao_maf">0.00</span> V</div>
      <div class="status-item"><strong>Valor Analógico:</strong> <span id="analog_maf">0</span></div>
      <div class="status-item"><strong>Velocidade Ar:</strong> <span id="velocidade_ar">0.00</span> m/s</div>
      <div class="status-item"><strong>Fluxo/s:</strong> <span id="fluxo_s">0.00</span> m³/s</div>
      <div class="status-item"><strong>Fluxo/min:</strong> <span id="fluxo_m">0.00</span> m³/min</div>
      <div class="status-item"><strong>Fluxo/h:</strong> <span id="fluxo_h">0.00</span> m³/h</div>
      <div class="status-item"><strong>Volume Acumulado:</strong> <span id="acumulado">0.00</span> m³</div>
    </div>
  </div>

  <div style="margin-top: 15px;">
    <strong>Hora:</strong> <span id="hora">0</span><br>
    <div id="retorno" style="margin-top: 10px;">Carregando status...</div>
  </div>
</div>
</div>
<script>
var conter = 0;
var modoAtual = "simples";
var button_configurar = document.getElementById("button_configurar");
var button_abrir = document.getElementById("button_abrir");
var button_fechar = document.getElementById("button_fechar");
var button_parar = document.getElementById("button_parar");
var button_zerar = document.getElementById("button_zerar");
var button_teste = document.getElementById("button_teste");
button_configurar.onclick = configurarModo;
button_fechar.onclick = myfunction;
button_abrir.onclick = myfunction;
button_parar.onclick = myfunction;
button_zerar.onclick = myfunction;
button_teste.onclick = myfunction;

function configurarModo() {
  var modo = document.getElementById("modoSelect").value;
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      try {
        var response = JSON.parse(this.responseText);
        if (response.status === "ok") {
          alert("Modo configurado com sucesso!\nModo: " + response.modo + "\nDispositivo: " + response.dispositivo);
          carregarModoAtual();
        }
      } catch(e) {
        alert("Erro ao configurar modo");
      }
    }
  };
  xhttp.open("GET", "setModo?modo=" + modo, true);
  xhttp.send();
}

function carregarModoAtual() {
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      try {
        var response = JSON.parse(this.responseText);
        modoAtual = response.modo;
        document.getElementById("modoSelect").value = response.modo;
        document.getElementById("modoAtual").innerHTML = response.modo === "simples" ? "Válvula Simples" : "Válvula Cabine";
        document.getElementById("nomeDispositivo").innerHTML = response.dispositivo;
        document.getElementById("texto").innerHTML = response.dispositivo;
        document.getElementById("titulo").innerHTML = response.dispositivo;
      } catch(e) {
        console.log("Erro ao carregar modo atual");
      }
    }
  };
  xhttp.open("GET", "getModo", true);
  xhttp.send();
}

function atualizarDados() {
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      try {
        var dados = JSON.parse(this.responseText);

        // Atualiza dados básicos do equipamento
        document.getElementById("ip").innerHTML = dados.ip;
        document.getElementById("mac").innerHTML = dados.mac;
        document.getElementById("uptime").innerHTML = dados.uptime;
        document.getElementById("status_valvula").innerHTML = dados.status_valvula;
        document.getElementById("operacoes").innerHTML = dados.operacoes;
        document.getElementById("rede").innerHTML = dados.rede;

        // Atualiza informações de operação
        var ultimaOp = dados.ultima_operacao || "Nenhuma";
        if (dados.timeout_atingido) {
          document.getElementById("ultima_operacao").innerHTML = "<span style='color: red;'>" + ultimaOp + "</span>";
        } else if (ultimaOp !== "Nenhuma") {
          document.getElementById("ultima_operacao").innerHTML = "<span style='color: green;'>" + ultimaOp + "</span>";
        } else {
          document.getElementById("ultima_operacao").innerHTML = ultimaOp;
        }

        // Atualiza dados MAF se modo CABINE
        var dadosMAF = document.getElementById("dados-maf");
        if (dados.modo === "cabine" && dados.maf) {
          dadosMAF.style.display = "block";
          document.getElementById("tensao_maf").innerHTML = dados.maf.tensao.toFixed(2);
          document.getElementById("analog_maf").innerHTML = Math.round(dados.maf.analog);
          document.getElementById("velocidade_ar").innerHTML = dados.maf.velocidade.toFixed(2);
          document.getElementById("fluxo_s").innerHTML = dados.maf.fluxo_s.toFixed(4);
          document.getElementById("fluxo_m").innerHTML = dados.maf.fluxo_m.toFixed(2);
          document.getElementById("fluxo_h").innerHTML = dados.maf.fluxo_h.toFixed(1);
          document.getElementById("acumulado").innerHTML = dados.maf.acumulado.toFixed(2);
        } else {
          dadosMAF.style.display = "none";
        }

      } catch(e) {
        console.log("Erro ao carregar dados: " + e);
      }
    }
  };
  xhttp.open("GET", "getDados", true);
  xhttp.send();
}
function myfunction() {
  var name = this.getAttribute('data-name');

  // Feedback visual
  if (name === "abrir") {
    document.getElementById("ultima_operacao").innerHTML = "<span style='color: blue;'>Abrindo válvula...</span>";
  } else if (name === "fechar") {
    document.getElementById("ultima_operacao").innerHTML = "<span style='color: blue;'>Fechando válvula...</span>";
  }

  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      console.log("Comando executado: " + name);
      // Força atualização dos dados após comando
      setTimeout(atualizarDados, 100);
    }
  };

  xhttp.open("GET", "setValvula?Comando="+name, true);
  xhttp.send();
}
// Carrega o modo atual na inicialização
carregarModoAtual();

// Atualiza dados a cada 2 segundos
setInterval(function() {
  atualizarDados();
  getData();
}, 2000); //2000mSeconds update rate

// Primeira atualização imediata
setTimeout(atualizarDados, 500);
function getData() {
  var xhttp = new XMLHttpRequest();

  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
     var received = this.responseText;
      received = received.replace(/\n/g, "<br />");

      document.getElementById("retorno").innerHTML = received;
    }
    conter ++;
    startTime();
  };
  xhttp.open("GET", "readMsg", true);
  xhttp.send();
}
function startTime() {
  var today = new Date();
  var h = today.getHours();
  var m = today.getMinutes();
  var s = today.getSeconds();
  document.getElementById("hora").innerHTML =
  checkTime(h) + ":" + checkTime(m) + ":" + checkTime(s);
//  var t = setTimeout(startTime, 500);
}
function checkTime(i) {
  if (i < 10) {i = "0" + i};  // add zero in front of numbers < 10
  return i;
}
</script>
<br><br><a href="http://www.vilatec.com.br">Evolution.com</a>
</body>
</html>
)=====";
