const char MAIN_page[] PROGMEM = R"=====(
<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body { font-family: Arial; margin: 20px; background-color: #f0f0f0; }
    .container { max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .modo-config { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin-bottom: 20px; border: 2px solid #2196F3; }
    .button { padding: 12px 20px; font-size: 16px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; min-width: 150px; }
    .button-primary { background-color: #2196F3; color: white; }
    .button-success { background-color: #4CAF50; color: white; }
    .button-warning { background-color: #ff9800; color: white; }
    .button-danger { background-color: #f44336; color: white; }
    .status-info { background-color: #f9f9f9; padding: 10px; border-radius: 5px; margin-top: 15px; font-family: monospace; }
    select { padding: 8px; font-size: 16px; margin: 5px; border-radius: 3px; border: 1px solid #ccc; }
    h1 { color: #2196F3; text-align: center; }
    .modo-atual { font-weight: bold; color: #2196F3; }
  </style>
</head>
<body>
<title id="titulo">Controle de Válvula</title>
<div class="container">
<h1 id="texto">Controle de Válvula IoT</h1>

<!-- Configuração de Modo -->
<div class="modo-config">
  <h3>Configuração do Modo de Operação</h3>
  <label for="modoSelect">Selecione o modo:</label>
  <select id="modoSelect">
    <option value="simples">Válvula Simples</option>
    <option value="cabine">Válvula Cabine</option>
  </select>
  <button class="button button-primary" type="button" id="button_configurar">CONFIGURAR</button>
  <div style="margin-top: 10px;">
    <strong>Modo Atual:</strong> <span id="modoAtual" class="modo-atual">Carregando...</span><br>
    <strong>Dispositivo:</strong> <span id="nomeDispositivo" class="modo-atual">Carregando...</span>
  </div>
</div>

<!-- Controles da Válvula -->
<div>
  <button class="button button-success" type="button" id="button_abrir" data-name="abrir">ABRIR VÁLVULA</button><BR>
  <button class="button button-danger" type="button" id="button_fechar" data-name="fechar">FECHAR VÁLVULA</button><BR>
  <button class="button button-warning" type="button" id="button_teste" data-name="teste">TESTE 100X</button><BR>
  <button class="button button-warning" type="button" id="button_parar" data-name="parar">PARAR</button><BR>
  <button class="button button-primary" type="button" id="button_zerar" data-name="zerar">ZERAR CONTAGEM</button><BR>
</div>

<!-- Status do Sistema -->
<div class="status-info">
  <strong>Hora:</strong> <span id="hora">0</span><br>
  <div id="retorno">Carregando status...</div>
</div>
</div>
<script>
var conter = 0;
var modoAtual = "simples";
var button_configurar = document.getElementById("button_configurar");
var button_abrir = document.getElementById("button_abrir");
var button_fechar = document.getElementById("button_fechar");
var button_parar = document.getElementById("button_parar");
var button_zerar = document.getElementById("button_zerar");
var button_teste = document.getElementById("button_teste");
button_configurar.onclick = configurarModo;
button_fechar.onclick = myfunction;
button_abrir.onclick = myfunction;
button_parar.onclick = myfunction;
button_zerar.onclick = myfunction;
button_teste.onclick = myfunction;

function configurarModo() {
  var modo = document.getElementById("modoSelect").value;
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      try {
        var response = JSON.parse(this.responseText);
        if (response.status === "ok") {
          alert("Modo configurado com sucesso!\nModo: " + response.modo + "\nDispositivo: " + response.dispositivo);
          carregarModoAtual();
        }
      } catch(e) {
        alert("Erro ao configurar modo");
      }
    }
  };
  xhttp.open("GET", "setModo?modo=" + modo, true);
  xhttp.send();
}

function carregarModoAtual() {
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      try {
        var response = JSON.parse(this.responseText);
        modoAtual = response.modo;
        document.getElementById("modoSelect").value = response.modo;
        document.getElementById("modoAtual").innerHTML = response.modo === "simples" ? "Válvula Simples" : "Válvula Cabine";
        document.getElementById("nomeDispositivo").innerHTML = response.dispositivo;
        document.getElementById("texto").innerHTML = response.dispositivo;
        document.getElementById("titulo").innerHTML = response.dispositivo;
      } catch(e) {
        console.log("Erro ao carregar modo atual");
      }
    }
  };
  xhttp.open("GET", "getModo", true);
  xhttp.send();
}
function myfunction() {
  var name = this.getAttribute('data-name');

  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      console.log("Comando executado: " + name);
    }
  };

  xhttp.open("GET", "setValvula?Comando="+name, true);
  xhttp.send();
}
setInterval(function() {
  // Call a function repetatively with 2 Second interval
  getData();
}, 2000); //2000mSeconds update rate

// Carrega o modo atual na inicialização
carregarModoAtual();
function getData() {
  var xhttp = new XMLHttpRequest();

  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
     var received = this.responseText;
      received = received.replace(/\n/g, "<br />");

      document.getElementById("retorno").innerHTML = received;
    }
    conter ++;
    startTime();
  };
  xhttp.open("GET", "readMsg", true);
  xhttp.send();
}
function startTime() {
  var today = new Date();
  var h = today.getHours();
  var m = today.getMinutes();
  var s = today.getSeconds();
  document.getElementById("hora").innerHTML =
  checkTime(h) + ":" + checkTime(m) + ":" + checkTime(s);
//  var t = setTimeout(startTime, 500);
}
function checkTime(i) {
  if (i < 10) {i = "0" + i};  // add zero in front of numbers < 10
  return i;
}
</script>
<br><br><a href="http://www.vilatec.com.br">Evolution.com</a>
</body>
</html>
)=====";
