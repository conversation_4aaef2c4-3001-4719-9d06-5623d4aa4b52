const char MAIN_page[] PROGMEM = R"=====(
<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
</head>
<body>
<title id="titulo">Device</title>
<div>
<h1 id="texto" style="color:blue;">Device</h1>
  <input type="text" id="input">
  <button style="width:150px;background-color:blue;" type="button" id="button_gravaname">Grava nome</button><BR>
  <button type="button" id="button_teste" data-name="teste">TESTE 100X</button><BR>
  <button type="button" id="button_abrir" data-name="abrir" >ABRIR VALUVULA</button><BR>
  <button type="button" id="button_fechar" data-name="fechar">FECHAR eeprom_parametros.dispositivo</button><BR>
  <button type="button" id="button_parar" data-name="parar">PARAR</button><BR>
  <button type="button" id="button_zerar" data-name="zerar">ZERA CONTAGEM</button><BR>
</div>
<div>
<span id="hora">0</span><br>
<span id="retorno">0</span><br>
 </div>
<script>
var conter = 0;
var names=["",""];
var button_grava = document.getElementById("button_gravaname");
var button_abrir = document.getElementById("button_abrir");
var button_fechar = document.getElementById("button_fechar");
var button_parar = document.getElementById("button_parar");
var button_zerar = document.getElementById("button_zerar");
var button_teste = document.getElementById("button_teste");
button_grava.onclick = sendName;
button_fechar.onclick = myfunction;
button_abrir.onclick = myfunction;
button_parar.onclick = myfunction;
button_zerar.onclick = myfunction;
button_teste.onclick = myfunction;

function sendName() {
  var x = document.getElementById("input").value
  var id = document.getElementById("texto").value
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
  if (this.readyState == 4 && this.status == 200) {
      document.getElementById("Comando").innerHTML = this.responseText;
  }
  };
  if(names[0] == names[1]){
  xhttp.open("GET", "setName?Name="+x);
  xhttp.send();
  location.reload(); 
  }
  else{
    alert("O nome do dispositivo já foi definido!");
  }
}
function myfunction() {
  var name = this.getAttribute('data-name');
  
    var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      document.getElementById("Comando").innerHTML =
      this.responseText;
    }
  };
  
  xhttp.open("GET", "seteeprom_parametros.dispositivo?Comando="+name, true);
  xhttp.send();
}
setInterval(function() {
  // Call a function repetatively with 2 Second interval
  getData();
}, 2000); //2000mSeconds update rate

function sendTitle() {
  var xhttp = new XMLHttpRequest();
  
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      //alert("ok");
      names[0] = this.responseText.split(",")[0];
      names[1] = this.responseText.split(",")[1];
      document.getElementById("titulo").innerHTML = names[1];
      document.getElementById("texto").innerHTML = names[1];
    }
  };
  xhttp.open("GET", "sendTitle", true);
  xhttp.send();
}
sendTitle();
function getData() {
  var xhttp = new XMLHttpRequest();
  
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
     //alert("ok");
     var received = this.responseText;
      received = received.replace(/\n/g, "<br />");
      
      document.getElementById("retorno").innerHTML = received;
    }
    conter ++;
    startTime();
  };
  xhttp.open("GET", "readMsg", true);
  xhttp.send();
}
function startTime() {
  var today = new Date();
  var h = today.getHours();
  var m = today.getMinutes();
  var s = today.getSeconds();
  document.getElementById("hora").innerHTML =
  checkTime(h) + ":" + checkTime(m) + ":" + checkTime(s);
//  var t = setTimeout(startTime, 500);
}
function checkTime(i) {
  if (i < 10) {i = "0" + i};  // add zero in front of numbers < 10
  return i;
}
</script>
<br><br><a href="http://www.vilatec.com.br">Evolution.com</a>
</body>
</html>
)=====";
