# Documentação Técnica - Sistema de Controle de Válvula/Cabine IoT
## Versão 1.1.1

---

## 1. VISÃO GERAL DO SISTEMA

Este projeto implementa um sistema IoT baseado em ESP8266 (WEMOS D1 Mini) para controle de dispositivos eletromecânicos que podem operar em dois modos distintos:

### 1.1 Modo Válvula Simples
- **Função**: Controle básico de abertura e fechamento de válvulas pneumáticas ou hidráulicas
- **Características**: Operação binária (aberta/fechada) com sensores de fim de curso
- **Aplicação**: Sistemas de automação industrial, controle de fluxo, irrigação automatizada

### 1.2 Modo Cabine (Válvula com Sensor MAF)
- **Função**: Controle avançado de válvulas com medição de fluxo de ar
- **Características**: Inclui sensor MAF (Mass Air Flow) para monitoramento de velocidade e volume de ar
- **Aplicação**: Sistemas HVAC, cabines de pintura, controle de ventilação industrial

---

## 2. ARQUITETURA DO SISTEMA

### 2.1 Componentes Principais
- **Microcontrolador**: ESP8266 (WEMOS D1 Mini)
- **Conectividade**: WiFi 802.11 b/g/n
- **Protocolo de Comunicação**: MQTT
- **Interface Web**: Servidor HTTP integrado
- **Armazenamento**: EEPROM para configurações persistentes

### 2.2 Funcionalidades Core
- Controle remoto via MQTT
- Interface web para configuração local
- Monitoramento de status em tempo real
- Contagem de operações (ciclos de abertura/fechamento)
- Sistema de calibração para sensor MAF
- Atualização OTA (Over-The-Air)

---

## 3. CONFIGURAÇÃO DO MODO DE OPERAÇÃO

### 3.1 Determinação Automática do Modo
O sistema determina automaticamente o modo de operação baseado no nome do dispositivo:

```cpp
if (strstr(eeprom_parametros.dispositivo, "CABINE")) {
    valvula_cabine = true;  // Ativa modo cabine
}
```

### 3.2 Configuração para Modo Válvula Simples
**Nome do Dispositivo**: Deve conter "VALVULA" (ex: "VALVULA_AZ_2B")

**Características Ativas**:
- Controle básico de abertura/fechamento
- Sensores de fim de curso
- Contagem de operações
- Interface web simplificada
- Comunicação MQTT básica

**Configurações Necessárias**:
- Nome do dispositivo sem "CABINE"
- Servidor MQTT configurado
- Sensores de fim de curso conectados

### 3.3 Configuração para Modo Cabine
**Nome do Dispositivo**: Deve conter "CABINE" (ex: "CABINE_PINTURA_01")

**Características Ativas**:
- Todas as funcionalidades do modo válvula simples
- Leitura do sensor MAF (Mass Air Flow)
- Cálculo de velocidade do ar (m/s)
- Medição de volume de ar (m³/s, m³/min, m³/h)
- Sistema de calibração por pontos
- Monitoramento contínuo de fluxo

**Configurações Necessárias**:
- Nome do dispositivo contendo "CABINE"
- Sensor MAF conectado ao pino A0
- Calibração dos pontos de medição
- Configuração da área da seção transversal (padrão: π × (0.072²/4))

---

## 4. SISTEMA DE SENSORES E ATUADORES

### 4.1 Pinos de Controle da Válvula
```cpp
#define abre_valvula_1 0      // GPIO0 (D3) - Comando para abrir
#define fecha_valvula_1 D2    // GPIO4 (D2) - Comando para fechar  
#define enable D4             // GPIO2 (D4) - Habilitação do driver
#define pwmPin 5              // GPIO14 (D5) - Controle PWM
```

### 4.2 Sensores de Fim de Curso
```cpp
#define valvula_aberta_1 D7   // GPIO13 (D7) - Sensor válvula aberta
#define valvula_fechada_1 D6  // GPIO12 (D6) - Sensor válvula fechada
```

### 4.3 Interface do Usuário
```cpp
#define botao D5              // GPIO14 (D5) - Botão manual
```

### 4.4 Sensor MAF (Apenas Modo Cabine)
- **Pino**: A0 (ADC)
- **Tensão de Referência**: 3.3V
- **Resolução**: 10 bits (0-1024)
- **Frequência de Leitura**: 200Hz (a cada 5ms)
- **Média**: 50 amostras por segundo

---

## 5. SISTEMA DE CALIBRAÇÃO MAF

### 5.1 Estrutura de Calibração
O sistema utiliza 11 pontos de calibração armazenados na EEPROM:
```cpp
double nodepoints[11][2] = {
    {valor_analogico, velocidade_ar},
    // ... 11 pontos total
};
```

### 5.2 Processo de Calibração
1. **Ponto Zero Automático**: Quando a válvula está fechada, o sistema automaticamente define o ponto zero
2. **Calibração Manual**: Via comando MQTT "CMAF,índice,valor"
3. **Validação**: O sistema verifica se os valores estão em ordem crescente
4. **Interpolação Linear**: Cálculo entre pontos usando função reMap()

### 5.3 Comandos de Calibração MQTT
- `GMAF`: Solicita envio dos pontos de calibração atuais
- `CMAF,2,5.5`: Define o ponto 2 com velocidade 5.5 m/s no valor atual do sensor
- `ZERA_M3`: Zera o acumulador de volume de ar

---

## 6. COMUNICAÇÃO MQTT

### 6.1 Configuração de Conexão
- **Servidor Padrão**: "MCC.local"
- **Porta Padrão**: 1883
- **Tópico de Publicação**: "MCC\\RET_VALVULA"
- **Tópico de Inscrição**: Nome do dispositivo

### 6.2 Estrutura das Mensagens JSON
```json
{
  "DISPOSITIVO": "NOME_DO_DISPOSITIVO",
  "STATUS": {
    "S_VALVULA": "ABERTA/FECHADA/ENTREABERTA/ERRO",
    "S_IP": "*************",
    "S_WIFI": 85,
    "S_MAC": "AA:BB:CC:DD:EE:FF",
    "S_VERSAO": "1.6/1.9",
    "S_UP TIME": "1 12:34:56",
    "S_CONTA_MOV": 1234
  }
}
```

### 6.3 Dados Adicionais Modo Cabine
```json
{
  "STATUS": {
    "S_VCC_MAF": 2.45,
    "S_VEL_AR_Ms": 12.5,
    "S_AR_M3s": 0.051,
    "S_AR_M3m": 3.06,
    "S_AR_M3h": 183.6,
    "S_SOMA_M3_AR": 15420.5,
    "S_AN_MAF": 512,
    "S_HAB_BOTAO": true
  }
}
```

---

## 7. COMANDOS DE CONTROLE REMOTO

### 7.1 Comandos Básicos
- `ABRE`: Abre a válvula
- `FECHA`: Fecha a válvula  
- `RESET`: Reinicia o dispositivo
- `OPERACOES`: Incrementa contador de operações
- `ZERA_OPERACOES`: Zera contador de operações
- `LOOP`: Ativa/desativa teste automático (100 ciclos)

### 7.2 Comandos Específicos Modo Cabine
- `HAB_BOT`: Habilita/desabilita botão físico
- `DEBUG`: Ativa/desativa modo debug
- `ZERA_M3`: Zera acumulador de volume de ar
- `GMAF`: Solicita dados de calibração
- `CMAF,índice,valor`: Calibra ponto específico

---

## 8. INTERFACE WEB

### 8.1 Funcionalidades da Interface
- **Visualização de Status**: Estado atual da válvula, IP, conectividade MQTT
- **Controle Manual**: Botões para abrir/fechar válvula
- **Teste Automático**: Execução de 100 ciclos de teste
- **Configuração**: Alteração de nome do dispositivo (se não fixo)
- **Reset**: Reinicialização remota do dispositivo

### 8.2 Endpoints Disponíveis
- `/`: Página principal
- `/readMsg`: Status atual em texto
- `/sendTitle`: Nome do dispositivo
- `/reset`: Reinicialização
- `/mqtt`: Configuração MQTT
- `/apagar`: Reset de configurações

---

## 9. SISTEMA DE ARMAZENAMENTO (EEPROM)

### 9.1 Estrutura de Dados
```cpp
struct eeprom_par {
  uint8_t version;              // Versão da estrutura
  bool is_root;                 // Flag de dispositivo raiz
  char dispositivo[20];         // Nome do dispositivo
  char mqtt_server[25];         // Servidor MQTT
  char mqtt_port[6];            // Porta MQTT
  bool habilita_botao;          // Habilitação do botão
  double nodepoints[11][2];     // Pontos de calibração MAF
};
```

### 9.2 Controle de Versão
- **Versão Atual**: 2
- **Migração Automática**: Sistema detecta versões antigas e migra dados
- **Backup**: Dados antigos preservados durante migração

---

## 10. ESQUEMA DE LIGAÇÃO ELETRÔNICA

### 10.1 WEMOS D1 Mini - Pinagem Principal
```
ESP8266 (WEMOS D1 Mini)
┌─────────────────────┐
│ RST          3V3    │ ← Alimentação 3.3V
│ A0           D8     │ ← Sensor MAF (A0)
│ D0           D7     │ ← Sensor Válvula Aberta
│ D1           D6     │ ← Sensor Válvula Fechada  
│ D2           D5     │ ← Botão Manual
│ D3           D4     │ ← Enable Driver
│ D4           GND    │ ← Terra
│ 3V3          5V     │ ← Alimentação 5V
└─────────────────────┘
```

### 10.2 Conexões dos Atuadores
```
Driver de Potência (ex: L298N, Ponte H)
┌─────────────────────┐
│ IN1 ← D3 (GPIO0)    │ ← Abre Válvula
│ IN2 ← D2 (GPIO4)    │ ← Fecha Válvula
│ ENA ← D4 (GPIO2)    │ ← Enable
│ PWM ← D5 (GPIO14)   │ ← Controle PWM
│ VCC ← 5V            │
│ GND ← GND           │
└─────────────────────┘
```

### 10.3 Sensores de Fim de Curso
```
Sensor Válvula Aberta (D7)
┌─────────────────────┐
│ VCC ← 3.3V          │
│ GND ← GND           │  
│ OUT ← D7 (GPIO13)   │ ← Sinal Digital
└─────────────────────┘

Sensor Válvula Fechada (D6)  
┌─────────────────────┐
│ VCC ← 3.3V          │
│ GND ← GND           │
│ OUT ← D6 (GPIO12)   │ ← Sinal Digital
└─────────────────────┘
```

### 10.4 Sensor MAF (Apenas Modo Cabine)
```
Sensor MAF
┌─────────────────────┐
│ VCC ← 3.3V          │
│ GND ← GND           │
│ OUT ← A0            │ ← Sinal Analógico (0-3.3V)
└─────────────────────┘
```

### 10.5 Botão Manual
```
Botão (D5)
┌─────────────────────┐
│ Terminal 1 ← D5     │ ← Pull-up interno ativo
│ Terminal 2 ← GND    │
└─────────────────────┘
```

---

## 11. CONSIDERAÇÕES DE SEGURANÇA

### 11.1 Proteções Implementadas
- **Timeout de Operação**: 2.8 segundos máximo por movimento
- **Verificação de Fim de Curso**: Impede operação se sensores não respondem
- **Watchdog WiFi**: Reinicialização automática em caso de desconexão prolongada
- **Validação de Comandos**: Verificação de estado antes de executar comandos

### 11.2 Recomendações de Instalação
- Utilizar fonte de alimentação estabilizada 5V/2A mínimo
- Implementar proteção contra surtos na alimentação
- Instalar fusíveis de proteção nos circuitos de potência
- Verificar isolação galvânica entre circuitos de controle e potência
- Implementar parada de emergência física independente do sistema

---

## 12. TROUBLESHOOTING

### 12.1 Problemas Comuns
- **Válvula não responde**: Verificar conexões do driver e alimentação
- **Sensor MAF com leituras erráticas**: Verificar calibração e conexões analógicas
- **Desconexão MQTT frequente**: Verificar estabilidade da rede WiFi
- **Contagem de operações incorreta**: Verificar sensores de fim de curso

### 12.2 Códigos de Status
- **"ABERTA"**: Válvula completamente aberta
- **"FECHADA"**: Válvula completamente fechada  
- **"ENTREABERTA"**: Válvula em posição intermediária
- **"ERRO"**: Ambos os sensores ativos simultaneamente (falha)

---

## 13. MANUTENÇÃO E ATUALIZAÇÕES

### 13.1 Atualização de Firmware
- **OTA Habilitado**: Atualizações remotas via rede
- **Interface ElegantOTA**: Acesso via navegador web
- **Backup Automático**: Configurações preservadas durante atualizações

### 13.2 Manutenção Preventiva
- Verificação periódica dos sensores de fim de curso
- Limpeza do sensor MAF (modo cabine)
- Verificação das conexões elétricas
- Teste dos ciclos de operação
- Backup das configurações de calibração

---

---

## 14. ESPECIFICAÇÕES TÉCNICAS DETALHADAS

### 14.1 Temporização do Sistema
- **Leitura Sensor MAF**: 5ms (200Hz)
- **Cálculo MAF**: 1000ms (1Hz)
- **Envio MQTT**: 1000ms (modo normal), 1000ms (durante operação)
- **Timeout Operação**: 2800ms por movimento
- **Reconexão MQTT**: 3000ms (falha), 15000ms (normal)
- **Watchdog WiFi**: Reset após 5 desconexões

### 14.2 Limites e Capacidades
- **EEPROM Total**: 1024 bytes
- **Pontos Calibração**: 11 pontos máximo
- **Buffer MQTT**: 512 bytes
- **Contador Operações**: Unsigned long (4.294.967.295 máximo)
- **Precisão MAF**: 10 bits (1024 níveis)
- **Área Seção Padrão**: π × (0.072²/4) m² = 0.00407 m²

### 14.3 Fórmulas de Cálculo MAF
```cpp
// Conversão ADC para tensão
voltage_sensor = media_maf * (3.3V / 1024.0);

// Cálculo volume de ar
metro_cubico_s_ar = velocidade_ar * (π × (0.072²/4));

// Acumulação por segundo
soma_metro_cubico_ar += metro_cubico_s_ar;
```

---

## 15. CONFIGURAÇÃO DE REDE E CONECTIVIDADE

### 15.1 Configuração WiFi Fixa
```cpp
const char* ssid = "mcc1";
const char* password = "##mcc1##";
```

### 15.2 Configuração MQTT Padrão
```cpp
char mqtt_server[25] = "MCC.local";
char mqtt_port[6] = "1883";
```

### 15.3 Hostname e mDNS
- **Hostname**: Nome do dispositivo configurado
- **mDNS**: Resolução local de nomes
- **Serviço HTTP**: Porta 80 anunciada via mDNS

---

## 16. ALGORITMO DE INTERPOLAÇÃO MAF

### 16.1 Função reMap()
O sistema utiliza interpolação linear entre pontos de calibração:

```cpp
float reMap(double pts[11][2], double input) {
  for (int a = 0; a < 11; a++) {
    if (input >= pts[a][0] && input <= pts[a+1][0]) {
      // Cálculo da inclinação
      mm = (pts[a][1] - pts[a+1][1]) / (pts[a][0] - pts[a+1][0]);
      // Interpolação linear
      mm = mm * (input - pts[a][0]) + pts[a][1];
      return mm;
    }
  }
  return 0.0;
}
```

### 16.2 Validação de Calibração
- **Ordem Crescente**: Valores analógicos devem ser crescentes
- **Ponto Zero**: Automaticamente definido com válvula fechada
- **Limites**: Valores fora da faixa são limitados aos extremos

---

## 17. ESTADOS DA MÁQUINA DE CONTROLE

### 17.1 Estados da Válvula
```cpp
// Lógica de determinação do estado
if (valvula_aberta == HIGH && valvula_fechada == LOW)
    status = "FECHADA";
else if (valvula_aberta == LOW && valvula_fechada == HIGH)
    status = "ABERTA";
else if (valvula_aberta == LOW && valvula_fechada == LOW)
    status = "ERRO";
else
    status = "ENTREABERTA";
```

### 17.2 Condições de Operação
- **Para Abrir**: Sensor fechada deve estar ativo (HIGH)
- **Para Fechar**: Sensor aberta deve estar ativo (HIGH)
- **Proteção**: Comando ignorado se condição não atendida

---

## 18. SISTEMA DE LOGS E MONITORAMENTO

### 18.1 Informações de Status Enviadas
- **Versão Firmware**: 1.6 (válvula simples), 1.9 (cabine)
- **Uptime**: Tempo desde última reinicialização
- **Qualidade WiFi**: Percentual baseado em RSSI
- **Endereço MAC**: Identificação única do dispositivo
- **Endereço IP**: IP atual na rede

### 18.2 Dados Específicos Modo Cabine
- **Tensão MAF**: Valor em volts do sensor
- **Velocidade Ar**: m/s calculada
- **Volume Instantâneo**: m³/s atual
- **Volume por Minuto**: m³/min
- **Volume por Hora**: m³/h
- **Volume Acumulado**: Total desde reset
- **Valor Analógico**: Leitura bruta ADC

---

## 19. PROCEDIMENTOS DE CONFIGURAÇÃO INICIAL

### 19.1 Primeira Inicialização
1. **Conexão WiFi**: Sistema conecta automaticamente à rede configurada
2. **Geração Nome**: Se não configurado, gera nome baseado no MAC
3. **Inicialização EEPROM**: Cria estruturas de dados padrão
4. **Detecção Modo**: Analisa nome para determinar modo operação
5. **Configuração Sensores**: Inicializa interrupções e timers

### 19.2 Configuração Modo Válvula Simples
1. Definir nome sem "CABINE"
2. Conectar sensores de fim de curso
3. Conectar driver de potência
4. Testar operação manual
5. Configurar servidor MQTT

### 19.3 Configuração Modo Cabine
1. Definir nome contendo "CABINE"
2. Executar configuração válvula simples
3. Conectar sensor MAF ao pino A0
4. Executar calibração inicial
5. Testar medições de fluxo

---

## 20. CONSIDERAÇÕES FINAIS

### 20.1 Limitações do Sistema
- **Conectividade**: Dependente de rede WiFi estável
- **Precisão MAF**: Limitada pela resolução ADC de 10 bits
- **Área Fixa**: Seção transversal hardcoded no código
- **Pontos Calibração**: Máximo 11 pontos de calibração

### 20.2 Possíveis Melhorias
- Implementação de rede mesh para maior confiabilidade
- Calibração automática com sensores de referência
- Interface web mais avançada com gráficos
- Suporte a múltiplos protocolos de comunicação
- Sistema de backup automático de configurações

### 20.3 Aplicações Recomendadas
- **Válvula Simples**: Automação residencial, irrigação, controle industrial básico
- **Modo Cabine**: Sistemas HVAC, cabines de pintura, laboratórios, salas limpas

---

*Documentação Técnica Completa*
*Projeto V_CAB_SIMP versão ********
*Data: 2025-09-22*
*Desenvolvido para ESP8266 (WEMOS D1 Mini)*
*Engenharia: Sistema IoT para Controle de Válvulas e Monitoramento de Fluxo*
