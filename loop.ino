void loop() {
  if (malvadeza.onRestart() && teste && operacoes_teste + 100 >= eepromVar.operacoes) {
    malvadeza.restart();
    if (alterna) {
      alterna = false;
      abre();
    } else {
      alterna = true;
      fecha();
    }
  }

  server.handleClient();
  ElegantOTA.loop();
  if (WiFi.status() != WL_CONNECTED) {
    wifiClient.stop();
    // espClient2.stop();
  } else {
    mqttClient.loop();
    // client2.loop();
  }



  if (timeReconnectMqtt.onRestart()) {
    MDNS.update();


    if (!mqttClient.connected() && WiFi.status() == WL_CONNECTED) {
      // char ipchar[ipaddress.length() + 1];
      // ipaddress.toCharArray(ipchar, ipaddress.length() + 1);
      Serial.println("Mqtt local desconectado ");
      Serial.println(WiFi.localIP().toString());
      Serial.println(eeprom_parametros.dispositivo);
      Serial.println(eeprom_parametros.mqtt_port);
      reconnectMqtt();
      timeReconnectMqtt.setTimeout(3000);
      //      if (!client.connected() && ! wifi_ap_sta) {
      //        wifi_ap_sta = true;
      //        WiFi.mode(WIFI_AP_STA);
      //        WiFi.softAP(strcat("AP_", eeprom_parametros.dispositivo), "acessoap");
      //      }
    } else {
      timeReconnectMqtt.setTimeout(15000);
    }
    timeReconnectMqtt.restart();
  }

  // if (!client2.connected() && WiFi.status() == WL_CONNECTED) {
  //   // char ipchar[ipaddress.length() + 1];
  //   // ipaddress.toCharArray(ipchar, ipaddress.length() + 1);
  //   Serial.println("Mqtt 2 servidor Evo ");
  //   Serial.println(WiFi.localIP().toString());
  //   Serial.println(eeprom_parametros.dispositivo);
  //   Serial.println("MCC.local");
  //   Serial.println(eeprom_parametros.mqtt_port);
  //   reconnectMqtt();
  //   //      if (!client.connected() && ! wifi_ap_sta) {
  //   //        wifi_ap_sta = true;
  //   //        WiFi.mode(WIFI_AP_STA);
  //   //        WiFi.softAP(strcat("AP_", eeprom_parametros.dispositivo), "acessoap");
  //   //      }
  // }




  unsigned long now = millis();
  if (valvula_cabine) {
    if (timer_calcula_maf.onRestart()) {
      CALCULA_MAF();
    }
    if (leitura_sensor.onRestart()) {  //PASSA POR AQUI A CADA 5 MS
      LER_MAF();
    }
  }
  valvula_1();
  if (button.onReleased() && valvula_cabine && eeprom_parametros.habilita_botao) {
    send_mqtt("S_TEMPO_OPERACAO", "BUTTON");
    if (digitalRead(valvula_aberta_1) == HIGH) {
      abre();
    } else {
      fecha();
    }
  }
  int sendCommand = 1000;
  if (abre_1 == true || fecha_1 == true || statusValvula_1 == "ABERTA") {
    sendCommand = 1000;
  }
  if (now - loop1 > sendCommand && count < 10) {
    loop1 = now;
    count++;
    DynamicJsonDocument doc(512);
    String ipaddress = WiFi.localIP().toString();
    char ipchar[ipaddress.length() + 1];
    ipaddress.toCharArray(ipchar, ipaddress.length() + 1);
    float RssI = WiFi.RSSI();
    RssI = isnan(RssI) ? -100.0 : RssI;
    RssI = min(max(2 * (RssI + 100.0), 0.0), 100.0);
    doc["STATUS"]["S_VALVULA"] = statusValvula_1;
    doc["STATUS"]["S_IP"] = ipchar;
    doc["STATUS"]["S_WIFI"] = RssI;
    doc["STATUS"]["S_MAC"] = MAC;
    doc["STATUS"]["S_VERSAO"] = "1.6";
    unsigned long currentMillis = millis();
    unsigned long seconds = currentMillis / 1000;
    unsigned long minutes = seconds / 60;
    unsigned long hours = minutes / 60;
    String readableTime;
    getReadableTime(readableTime);
    doc["STATUS"]["S_UP TIME"] = readableTime;
    doc["STATUS"]["S_CONTA_MOV"] = eepromVar.operacoes;




    if (valvula_cabine) {
      doc["STATUS"]["S_VERSAO"] = "1.9";
      doc["STATUS"]["S_HAB_BOTAO"] = eeprom_parametros.habilita_botao;
      // if (isnan(velocidade_ar)) {
      //   doc["STATUS"]["S_VCC_MAF"] =  "ERR";
      //   doc["STATUS"]["S_VEL_AR_Ms"] = "ERR";
      //   doc["STATUS"]["S_AR_M3s"] = "ERR";
      //   doc["STATUS"]["S_AR_M3m"] = "ERR";
      //   doc["STATUS"]["S_AR_M3h"] = "ERR";
      //   doc["STATUS"]["S_SOMA_M3_AR"] = "ERR";
      //   doc["STATUS"]["S_AN_MAF"] = "ERR";
      // } else {
      //   doc["STATUS"]["S_VCC_MAF"] = voltage_sensor;
      //   doc["STATUS"]["S_VEL_AR_Ms"] = velocidade_ar;
      //   doc["STATUS"]["S_AR_M3s"] = metro_cubico_s_ar;
      //   doc["STATUS"]["S_AR_M3m"] = metro_cubico_s_ar * 60;
      //   doc["STATUS"]["S_AR_M3h"] = metro_cubico_s_ar * 3600;
      //   doc["STATUS"]["S_SOMA_M3_AR"] = soma_metro_cubico_ar;
      //   doc["STATUS"]["S_AN_MAF"] = media_maf;
      // }
      doc["STATUS"]["S_VCC_MAF"] = voltage_sensor;
      doc["STATUS"]["S_VEL_AR_Ms"] = velocidade_ar;
      doc["STATUS"]["S_AR_M3s"] = metro_cubico_s_ar;
      doc["STATUS"]["S_AR_M3m"] = metro_cubico_s_ar * 60;
      doc["STATUS"]["S_AR_M3h"] = metro_cubico_s_ar * 3600;
      doc["STATUS"]["S_SOMA_M3_AR"] = soma_metro_cubico_ar;
      // doc["STATUS"]["S_MILLIS"] = millis();
      doc["STATUS"]["S_AN_MAF"] = media_maf;
    }
    doc["DISPOSITIVO"] = eeprom_parametros.dispositivo;
    // Cria um buffer de string para armazenar o JSON serializado
    String jsonString;
    serializeJson(doc, jsonString);

    // size_t jsonSize = measureJson(JSONencoder) + 1;  // +1 para o terminador nulo

    // // Criando o buffer para armazenar o JSON serializado
    // char buffer[jsonSize];

    // // Serializando o JSON no buffer
    // serializeJson(JSONencoder, buffer, jsonSize);

    // Publicando a mensagem MQTT

    // Serial.println(sucesso);
    // ;
    //  Serial.println();
    //  Serial.println(sizeof(JSONencoder));

    if (count >= 10) {
      // StaticJsonDocument<200> doc2;

      // // Adiciona os elementos ao objeto JSON
      // doc2["DISPOSITIVO"] = eeprom_parametros.dispositivo;
      // doc2["STATUS"]["S_eeprom_parametros.dispositivo"] = statusValvula_1;
      // if(mqttClient.connected()){
      //   doc2["STATUS"]["S_CON_MQTT"] = "CONECTADO";
      // }
      // else{
      //   doc2["STATUS"]["S_CON_MQTT"] = "DESCONECTADO";
      // }


      // String jsonString;
      // serializeJson(doc2, jsonString);

      // Publica o JSON
      // bool sucesso2 = client2.publish(TOPIC, jsonString.c_str());
      count = 0;
      if (valvula_cabine) {
        send_calib();
      }
    } else {
      bool sucesso2 = mqttClient.publish(TOPIC, jsonString.c_str());
    }
  }
}