//0, 6.6, 13.2, 19.8, 26,4, 33.0, 39.6,  46.2, 52.8,  59.4,  66.0
//0,1.57, 3.24, 4,82, 6.39, 7.94, 9.48, 11.15, 12.86, 14.76, 16.47
void LER_MAF() {
  read_sensor_maf += analogRead(A0);
  total_leitura_sensor ++;
  if (total_leitura_sensor >= 50) {
    media_maf = read_sensor_maf / total_leitura_sensor ;
    read_sensor_maf = 0.;
    total_leitura_sensor = 0;
  }
}
void CALCULA_MAF() {
  float voltage_vcc = 3.3;
  voltage_sensor = media_maf * (voltage_vcc / 1024.0);
  if (media_maf < eeprom_parametros.nodepoints[0][0] + 10) {
    media_maf = eeprom_parametros.nodepoints[0][0];
  }
  if (media_maf > eeprom_parametros.nodepoints[(sizeof(eeprom_parametros.nodepoints) / sizeof(eeprom_parametros.nodepoints[0])) -1][0]) {
    media_maf = eeprom_parametros.nodepoints[(sizeof(eeprom_parametros.nodepoints) / sizeof(eeprom_parametros.nodepoints[0])) -1][0];
  }  
  // if (digitalRead(valvula_fechada_1) == LOW && timer_eeprom_parametros.dispositivo_fechada.onRestart()){
  if (digitalRead(valvula_fechada_1) == LOW) {
    eeprom_parametros.nodepoints[0][0] = media_maf;
    eeprom_parametros.nodepoints[0][1] = 0.0;
  }  
  velocidade_ar = reMap(eeprom_parametros.nodepoints, media_maf);
  //Serial.print(metro_cubico_s_ar, 2);
  //Serial.print(" kg/h \t\t");
  metro_cubico_s_ar = velocidade_ar * (3.1415 * (pow(0.072, 2) / 4)); //converte para m³/s
  soma_metro_cubico_ar += metro_cubico_s_ar; //soma o valor em m³ uma vez por segundo

  //Serial.print(soma_metro_cubico_ar);
  // Serial.println(" m3\t\t");
  //Serial.println(WiFi.status());



  // send_mqtt("STATUS_TENSAO_MAF", String(voltage_sensor));

}

/*********************** CALCULA O NODEPOINS *************************/

float reMap(double pts[11][2], double input) {
  float bb, mm;

  for (int a = 0; a < sizeof(eeprom_parametros.nodepoints) / sizeof(eeprom_parametros.nodepoints[0]); a ++) {
    if (input >= pts[a][0] && input <= pts[a + 1][0]) {
      mm = ( pts[a][1] - pts[a + 1][1] ) / ( pts[a][0] - pts[a + 1][0] );
      mm = mm * (input - pts[a][0]);
      mm = mm +  pts[a][1];
      //      Serial.println(mm);
      return (mm);
    }
  }
  return 0.0;
}

void send_calib() {

  for (int a = 0; a < sizeof(eeprom_parametros.nodepoints) / sizeof(eeprom_parametros.nodepoints[0]); a ++) {
    StaticJsonDocument<256> bufferStatus;    
    const size_t CAPACITY2 = JSON_ARRAY_SIZE(2);
    StaticJsonDocument<CAPACITY2> doc2;
    JsonArray array2 = doc2.to<JsonArray>();
    array2.add(eeprom_parametros.nodepoints[a][0]);
    array2.add(eeprom_parametros.nodepoints[a][1]);
    bufferStatus["S_CALIB_" + String(a)] = doc2;
    StaticJsonDocument <256> JSONencoder;
    JSONencoder["DISPOSITIVO"] = eeprom_parametros.dispositivo;
    JSONencoder["STATUS"] = bufferStatus;
    size_t jsonSize = measureJson(JSONencoder) + 1;  // +1 para o terminador nulo

    // Criando o buffer para armazenar o JSON serializado
    char buffer[jsonSize];

    // Serializando o JSON no buffer
    serializeJson(JSONencoder, buffer, jsonSize);

    // Publicando a mensagem MQTT
    bool sucesso = mqttClient.publish(TOPIC, buffer);
    // bool sucesso2 = client2.publish(TOPIC, buffer);
  }


}
