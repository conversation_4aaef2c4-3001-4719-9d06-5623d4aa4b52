#include <ESP8266WiFi.h>
#include <PubSubClient.h>
#include <ESP_EEPROM.h>
#include <RBD_Timer.h>
#include <RBD_Button.h>
#include <ArduinoJson.h>
// #include <WiFiManager.h>
#include <ElegantOTA.h>
#include <WiFiClient.h>
#include <ESP8266WebServer.h>
#include <ESP8266mDNS.h>
#include <ESP8266HTTPUpdateServer.h>
#include <WiFiUdp.h>
#include <NTPClient.h>

// Cliente WiFi
WiFiClient wifiClient;
// WiFiClient espClient2;

// Instâncias da biblioteca MQTT
PubSubClient mqttClient(wifiClient);
// PubSubClient client2(espClient2);

double voltage_sensor = 0.;
byte conta_envio_serial = 0;
double media_maf = 0.;
double soma_metro_cubico_ar = 0.;
float metro_cubico_s_ar = 0.0;
double total_leitura_sensor = 0.;
double read_sensor_maf = 0.;
float velocidade_ar = 0.;
float A_;
float B_;
#include <string.h>
#include "progmem.h"
//#if   defined(ARDUINO_ESP8266_WEMOS_D1MINI)
//#define MODELO "MINI"
//#elif defined(ARDUINO_ESP8266_WEMOS_D1MINIPRO)
//#define MODELO "MINI PRO"
//#else
//#error Placa diferente da usual.
//#endif
 #define NOME_MANUAL "VALVULA_AZ_2B"
void ICACHE_RAM_ATTR abriu ();
void ICACHE_RAM_ATTR fechou ();
void fechou();
void abriu();
void abre();
void fecha();
void str_replace(char *src, char *oldchars, char *newchars);
void loop_eeprom();
void LER_MAF();
void CALCULA_MAF();
void send_calib();
void valvula_1();
void handleName();
void handleRoot();
void handleValvula();
void handleMsg();
void handleTitle();
void handleServerPort();
void handleApagar();
void atualizarNomeDispositivo();
void handleSetModo();
void handleGetModo();
void handleGetDados();
void getReadableTime(String &readableTime);
void configurarPontosCalibracaoMAF();
#define ATUADO 0
#define NAO_ATUADO 1
bool shouldSaveConfig = false;
#define MAC WiFi.macAddress()
// Controle de modo dinâmico
enum ModoOperacao { MODO_SIMPLES, MODO_CABINE };
ModoOperacao modo_atual = MODO_SIMPLES;
bool modo_alterado = false;

bool valvula_cabine = false;
bool debug_code = true;
bool alterna = false;
bool teste = false;
bool send_operacao_fechando = false;
bool send_operacao_abrindo = false;

// Variáveis para feedback de tempo de operação
String ultima_operacao = "";
unsigned long tempo_ultima_operacao = 0;
bool timeout_atingido = false;
RBD::Timer leitura_sensor(5);
RBD::Timer malvadeza;
RBD::Timer timeOffabre_valvula_1;
RBD::Timer timeOfffecha_valvula_1;
RBD::Timer timeReconnectMqtt;
RBD::Timer timer_calcula_maf(1000);
// #define MQTT_MAX_PACKET_SIZE 1024

const char* ssid = "mcc1";                                      // Substitua pelo nome da sua rede WiFi
const char* password = "##mcc1##";

WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, "gps.ntp.br", 3600, 60000);
ESP8266WebServer server(80);
// WiFiManager wifiManager;
//String HTMLpage = "";
#define LED 2
struct eeprom {
  unsigned long operacoes = 0;
  int index;
};
eeprom eepromVar;
byte conta_loop_reset = 0;
struct eeprom_par {
  uint8_t version;  // Campo de versão adicionado
  bool is_root;  // Campo de versão adicionado
  char dispositivo [20] = "";
  char mqtt_server [25] = "MCC.local";
  char mqtt_port [6] = "1883";
  bool habilita_botao = true;
  uint8_t modo_operacao = 0;  // 0 = SIMPLES, 1 = CABINE
  double nodepoints[11][2] = {
    {0., 0.},
    {0., 0.},
    {0., 0.},
    {0., 0.},
    {0., 0.},
    {0., 0.},
    {0., 0.},
    {0., 0.},
    {0., 0.},
    {0., 0.},
    {0., 0.}
  };
};
eeprom_par eeprom_parametros;
const uint8_t CURRENT_VERSION_STRUCT = 3;
int sizeof_struct_ant = 232;
int sizeof_eeprom;
int sizeof_eeprom_param;
#define MAC WiFi.macAddress()
char text[30];
#define TOPIC  "MCC\\RET_VALVULA"
unsigned long millisAnt1 = 0;
unsigned long duracao1 = 0;
unsigned long operacoes_teste = 0;
const char* statusValvula_1 = "###########";
unsigned long loop1 = 0;
int previousIndex = 0;
#define eepromSize 1024
int analogValue = 0;
bool abre_1 = false;
bool fecha_1 = false;
byte count = 0;
bool envia_mqtt_fechou = false;
bool wifiConnected = false;
//eeprom_parametros.dispositivo d2, d1
//eeprom_parametros.dispositivo 03 = gpio 01/TX, gpio 02/D4 eeprom_parametros.dispositivo reserva
//eeprom_parametros.dispositivo 02 = gpio 04/D2, gpio 05/D1 valvila 2
//eeprom_parametros.dispositivo 01 = gpio 00/D3, gpio 03/RX valvila 1
#define abre_valvula_1 0
#define fecha_valvula_1 D2
#define enable D4
#define pwmPin 5
#define valvula_aberta_1 D7
#define valvula_fechada_1 D6
#define botao D5
RBD::Button button(botao, false);
long lastMsg = 0;
bool wifi_ap_sta = false;
WiFiEventHandler gotIpEventHandler, disconnectedEventHandler;
