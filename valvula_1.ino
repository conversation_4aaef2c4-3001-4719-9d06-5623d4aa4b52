void abriu() {
  digitalWrite(abre_valvula_1, LOW);
  if (abre_1 == true) {
    abre_1 = false;
    digitalWrite(enable, LOW);
    analogWrite(pwmPin, 0);
    duracao1 = (millis() - millisAnt1) ;
    send_operacao_abrindo = true;
  }
}
void fechou() {
  digitalWrite(fecha_valvula_1, LOW);
  if (fecha_1 == true) {
    fecha_1 = false;
    digitalWrite(enable, LOW);
    analogWrite(pwmPin, 0);
    duracao1 = (millis() - millisAnt1) ;
    send_operacao_fechando = true;
  }
}
void valvula_1() {

  if (send_operacao_abrindo == true) {
    delay(50);
    if (digitalRead(valvula_aberta_1) == LOW && digitalRead(valvula_fechada_1) == HIGH) {
      eepromVar.operacoes ++;
      eepromWrite();
    }
    send_operacao_abrindo = false;

    // Registra tempo de operação para interface web
    ultima_operacao = "ABRINDO - " + String(duracao1) + "ms";
    tempo_ultima_operacao = duracao1;
    timeout_atingido = false;

    send_mqtt("S_TEMPO_OPERACAO", (String)"ABRINDO=" + duracao1);
  }

  if (send_operacao_fechando == true) {
    delay(50);
    if (digitalRead(valvula_aberta_1) == HIGH && digitalRead(valvula_fechada_1) == LOW) {
      eepromVar.operacoes ++;
      eepromWrite();
    }
    send_operacao_fechando = false;

    // Registra tempo de operação para interface web
    ultima_operacao = "FECHANDO - " + String(duracao1) + "ms";
    tempo_ultima_operacao = duracao1;
    timeout_atingido = false;

    send_mqtt("S_TEMPO_OPERACAO", (String)" FECHANDO=" + duracao1);
  }
  // Timeout para abertura (2800ms)
  if (timeOffabre_valvula_1.onRestart() && abre_1 == true) {
    digitalWrite(abre_valvula_1, LOW);
    digitalWrite(enable, LOW);
    analogWrite(pwmPin, 0);

    // Registra timeout para interface web
    ultima_operacao = "ABRINDO - TIMEOUT (2800ms)";
    tempo_ultima_operacao = 2800;
    timeout_atingido = true;

    send_mqtt("S_TEMPO_OPERACAO", "ABRINDO=TIMEOUT(2800ms)");
    abre_1 = false;
  }

  // Timeout para fechamento (2800ms)
  if (timeOfffecha_valvula_1.onRestart() && fecha_1 == true) {
    digitalWrite(fecha_valvula_1, LOW);
    digitalWrite(enable, LOW);
    analogWrite(pwmPin, 0);

    // Registra timeout para interface web
    ultima_operacao = "FECHANDO - TIMEOUT (2800ms)";
    tempo_ultima_operacao = 2800;
    timeout_atingido = true;

    send_mqtt("S_TEMPO_OPERACAO", "FECHANDO=TIMEOUT(2800ms)");
    fecha_1 = false;
  }

  if (digitalRead(valvula_aberta_1) == HIGH && digitalRead(valvula_fechada_1) == LOW) statusValvula_1 = "FECHADA";
  else if (digitalRead(valvula_aberta_1) == LOW && digitalRead(valvula_fechada_1) == HIGH) statusValvula_1 = "ABERTA";
  else if (digitalRead(valvula_aberta_1) == LOW && digitalRead(valvula_fechada_1) == LOW) statusValvula_1 = "ERRO";
  else statusValvula_1 = "ENTREABERTA";
}

void abre() {
  if (digitalRead(valvula_aberta_1) == HIGH) {
    digitalWrite(abre_valvula_1, HIGH);
    digitalWrite(enable, HIGH);
    analogWrite(pwmPin, 1024);
    timeOffabre_valvula_1.restart();
    timeOfffecha_valvula_1.restart();
    abre_1 = true;
    fecha_1 = false;
    millisAnt1 = millis();
  }
  else {
    send_mqtt("STATUS_COMANDO", "FC ATUADO");
  }
}

void fecha() {
  if (digitalRead(valvula_fechada_1) == HIGH) {
    digitalWrite(enable, HIGH);
    digitalWrite(fecha_valvula_1, HIGH);
    analogWrite(pwmPin, 1024);

    timeOfffecha_valvula_1.restart();
    timeOffabre_valvula_1.restart();
    abre_1 = false;
    fecha_1 = true;
    millisAnt1 = millis();
  }
  else {
    send_mqtt("STATUS_COMANDO", "FC ATUADO");
  }
}
